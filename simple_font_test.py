#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单字体测试脚本
"""

import pygame

def test_fonts():
    """测试字体"""
    pygame.init()
    
    # 测试字体列表
    chinese_fonts = [
        'microsoftyahei',  # 微软雅黑
        'simhei',          # 黑体
        'simsun',          # 宋体
        'kaiti',           # 楷体
        'fangsong',        # 仿宋
    ]
    
    print("测试中文字体...")
    
    working_fonts = []
    
    for font_name in chinese_fonts:
        try:
            font = pygame.font.SysFont(font_name, 24)
            # 测试渲染中文
            test_text = "关卡: 1"
            surface = font.render(test_text, True, (0, 0, 0))
            if surface.get_width() > 0:
                working_fonts.append(font_name)
                print(f"✓ {font_name} - 支持中文 (宽度: {surface.get_width()})")
            else:
                print(f"✗ {font_name} - 不支持中文")
        except Exception as e:
            print(f"✗ {font_name} - 加载失败: {e}")
    
    pygame.quit()
    
    if working_fonts:
        print(f"\n找到 {len(working_fonts)} 个可用的中文字体: {', '.join(working_fonts)}")
        return True
    else:
        print("\n未找到可用的中文字体")
        return False

if __name__ == "__main__":
    test_fonts()
