基于python的推箱子游戏设计
组员：熊秋锦   廖云川  叶马可
一、设计目的与意义
推箱子（Sokoban）是一款经典的益智类逻辑游戏，具有规则简单、玩法严谨、挑战性强等特点，广泛应用于算法训练、逻辑思维培养和人机交互教学中。本项目旨在通过 Python 语言实现一个具备完整交互功能的推箱子游戏，一方面巩固面向对象编程、事件驱动机制和图形界面开发等核心编程技能，另一方面提升对游戏状态管理、路径判断及用户交互逻辑的理解。同时，该项目可作为计算机基础课程或游戏开发入门的实践案例，具有良好的教学示范价值和可扩展性，为后续开发更复杂的策略类或解谜类游戏奠定基础。

二、基本功能
1.初始化游戏窗口与角色、箱子、墙壁及目标点的地图布局；
2.键盘控制角色上下左右移动，推动相邻箱子并在合法位置移动；
3.角色推动箱子至目标点后，若所有箱子均覆盖目标点则关卡完成、得分或步数记录更新；
4.检测角色越界、非法推动（如推两箱或推墙）等无效操作时忽略输入，确保游戏规则正确性；
5.实时刷新界面并显示当前关卡、移动步数及已完成目标数量。

三、设计思路与技术方案
本项目采用模块化设计思想，将地图数据、游戏角色、用户输入、渲染显示和游戏逻辑分离处理。使用 Python 的 Pygame 库构建图形窗口并处理键盘事件，实现流畅的用户交互；游戏地图以二维列表形式存储，每种元素（如墙壁、空地、箱子、目标点、玩家）用不同字符或整数标识，便于状态判断与更新；移动逻辑通过方向向量计算目标位置，结合条件判断实现“可走”“可推”“不可动”等行为；游戏胜利条件通过遍历所有目标点是否被箱子占据来判定；界面采用精灵（Sprite）或图块（Tile）方式绘制，保证视觉清晰；帧率控制使用 pygame.time.Clock() 维持 30 FPS，确保操作响应及时且画面稳定。关卡数据可从外部文本文件加载，支持后续扩展多关卡系统。

四、预期效果
程序运行后将弹出游戏窗口，展示第一关地图，玩家可通过方向键控制角色移动并推动箱子。界面顶部实时显示当前关卡编号、已移动步数及已归位箱子数量。当所有箱子准确推入目标点时，自动提示“关卡完成”并可进入下一关。任何非法操作（如撞墙、试图推动两个箱子、将箱子推入死角等）将被系统忽略，确保游戏规则严格遵守。整体界面简洁美观，操作流畅，具备良好的用户体验和可玩性。

五、成员分工
熊秋锦：负责游戏主逻辑设计、地图数据结构实现及胜负判定模块开发。
廖云川：负责 Pygame 窗口初始化、键盘事件处理、角色与地图渲染及界面信息显示。
叶马可：负责关卡文件设计、移动合法性检测、步数记录功能及整体测试与调试。
