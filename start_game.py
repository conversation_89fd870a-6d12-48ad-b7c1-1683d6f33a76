#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
推箱子游戏一键启动脚本
"""

import os
import sys
import subprocess

def check_requirements():
    """检查游戏运行要求"""
    print("🔍 检查游戏运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("❌ Python版本过低，需要Python 3.6或更高版本")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查pygame
    try:
        import pygame
        print(f"✅ Pygame版本: {pygame.version.ver}")
    except ImportError:
        print("❌ 未安装pygame")
        print("请运行: pip install pygame")
        return False
    
    # 检查必要文件
    required_files = [
        "main.py",
        "sokoban_game.py",
        "levels/level_1.txt",
        "levels/level_2.txt",
        "levels/level_3.txt",
        "levels/level_4.txt",
        "levels/level_5.txt"
    ]
    
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ 缺少文件: {file}")
            return False
    
    print("✅ 所有必要文件存在")
    
    # 检查素材文件
    image_files = [
        "image/wall.png",
        "image/box.png", 
        "image/boxfinished.png",
        "image/destination.png",
        "image/character.png"
    ]
    
    missing_images = []
    for file in image_files:
        if not os.path.exists(file):
            missing_images.append(file)
    
    if missing_images:
        print("⚠️  缺少素材文件，将使用颜色块显示:")
        for file in missing_images:
            print(f"   - {file}")
    else:
        print("✅ 所有素材文件存在")
    
    return True

def start_game():
    """启动游戏"""
    print("\n🎮 启动推箱子游戏...")
    
    try:
        # 运行游戏
        subprocess.run([sys.executable, "main.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ 游戏启动失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n👋 游戏已退出")
        return True
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("🎯 推箱子游戏 - 一键启动")
    print("=" * 50)
    
    # 检查环境
    if not check_requirements():
        print("\n❌ 环境检查失败，无法启动游戏")
        input("按回车键退出...")
        return
    
    print("\n✅ 环境检查通过！")
    
    # 显示游戏说明
    print("\n📖 游戏说明:")
    print("- 方向键/WASD: 移动角色")
    print("- R键: 重置关卡")
    print("- N键: 下一关")
    print("- P键: 上一关")
    print("- ESC键: 退出游戏")
    
    input("\n按回车键开始游戏...")
    
    # 启动游戏
    start_game()

if __name__ == "__main__":
    main()
