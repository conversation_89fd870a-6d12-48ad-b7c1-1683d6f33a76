#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查素材文件
"""

import pygame
import os

def check_sprites():
    """检查素材文件"""
    pygame.init()
    
    sprite_files = [
        'image/wall.png',
        'image/box.png', 
        'image/boxfinished.png',
        'image/destination.png',
        'image/character.png'
    ]
    
    print("检查素材文件...")
    
    for file_path in sprite_files:
        if os.path.exists(file_path):
            try:
                img = pygame.image.load(file_path)
                print(f"✓ {file_path}: {img.get_width()}x{img.get_height()} 像素")
            except Exception as e:
                print(f"✗ {file_path}: 加载失败 - {e}")
        else:
            print(f"✗ {file_path}: 文件不存在")
    
    pygame.quit()

if __name__ == "__main__":
    check_sprites()
