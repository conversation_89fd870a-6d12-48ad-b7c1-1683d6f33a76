#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字体测试脚本
"""

import pygame
import sys

def test_chinese_font():
    """测试中文字体显示"""
    pygame.init()
    
    # 创建测试窗口
    screen = pygame.display.set_mode((600, 400))
    pygame.display.set_caption("中文字体测试")
    
    # 测试字体列表
    chinese_fonts = [
        'microsoftyahei',  # 微软雅黑
        'simhei',          # 黑体
        'simsun',          # 宋体
        'kaiti',           # 楷体
        'fangsong',        # 仿宋
    ]
    
    print("测试中文字体...")
    
    working_fonts = []
    
    for font_name in chinese_fonts:
        try:
            font = pygame.font.SysFont(font_name, 24)
            # 测试渲染中文
            test_text = "关卡: 1 步数: 0 进度: 0/1"
            surface = font.render(test_text, True, (0, 0, 0))
            if surface.get_width() > 0:
                working_fonts.append(font_name)
                print(f"✓ {font_name} - 支持中文")
            else:
                print(f"✗ {font_name} - 不支持中文")
        except Exception as e:
            print(f"✗ {font_name} - 加载失败: {e}")
    
    if working_fonts:
        print(f"\n找到 {len(working_fonts)} 个可用的中文字体")
        
        # 显示测试
        screen.fill((255, 255, 255))
        y_pos = 50
        
        for font_name in working_fonts:
            font = pygame.font.SysFont(font_name, 24)
            text = font.render(f"{font_name}: 关卡: 1 步数: 0 进度: 0/1", True, (0, 0, 0))
            screen.blit(text, (50, y_pos))
            y_pos += 40
        
        # 显示说明
        default_font = pygame.font.Font(None, 24)
        instruction = default_font.render("Press any key to close", True, (0, 0, 0))
        screen.blit(instruction, (50, y_pos + 20))
        
        pygame.display.flip()
        
        # 等待用户输入
        waiting = True
        while waiting:
            for event in pygame.event.get():
                if event.type == pygame.QUIT or event.type == pygame.KEYDOWN:
                    waiting = False
    else:
        print("未找到可用的中文字体")
    
    pygame.quit()
    return len(working_fonts) > 0

if __name__ == "__main__":
    success = test_chinese_font()
    if success:
        print("\n中文字体测试成功！")
    else:
        print("\n中文字体测试失败！")
        sys.exit(1)
