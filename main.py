#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
推箱子游戏主程序
作者：熊秋锦、廖云川、叶马可
"""

import pygame
import sys
import os
from sokoban_game import SokobanGame

# 游戏配置
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 600
FPS = 30
TITLE = "推箱子游戏 - Sokoban"

def main():
    """游戏主函数"""
    # 初始化Pygame
    pygame.init()
    
    # 创建游戏窗口
    screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
    pygame.display.set_caption(TITLE)
    
    # 创建时钟对象控制帧率
    clock = pygame.time.Clock()
    
    # 创建游戏实例
    try:
        game = SokobanGame(screen)
    except Exception as e:
        print(f"游戏初始化失败: {e}")
        pygame.quit()
        sys.exit(1)
    
    # 游戏主循环
    running = True
    while running:
        # 处理事件
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                # 处理键盘输入
                if event.key == pygame.K_ESCAPE:
                    running = False
                elif event.key == pygame.K_r:
                    # R键重置当前关卡
                    game.reset_level()
                elif event.key == pygame.K_n:
                    # N键下一关
                    if game.next_level():
                        print(f"进入关卡 {game.current_level}")
                    else:
                        print("已经是最后一关")
                elif event.key == pygame.K_p:
                    # P键上一关
                    if game.previous_level():
                        print(f"返回关卡 {game.current_level}")
                    else:
                        print("已经是第一关")
                elif event.key in [pygame.K_UP, pygame.K_DOWN, pygame.K_LEFT, pygame.K_RIGHT,
                                 pygame.K_w, pygame.K_s, pygame.K_a, pygame.K_d]:
                    # 只有方向键才传递给handle_input
                    game.handle_input(event.key)
        
        # 更新游戏状态
        game.update()
        
        # 渲染游戏画面
        game.render()
        
        # 更新显示
        pygame.display.flip()
        
        # 控制帧率
        clock.tick(FPS)
    
    # 退出游戏
    pygame.quit()
    sys.exit()

if __name__ == "__main__":
    main()
