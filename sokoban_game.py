#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
推箱子游戏核心类
包含游戏逻辑、地图管理、状态控制等功能
"""

import pygame
import os
from typing import List, Tuple, Optional

# 地图元素定义
EMPTY = 0      # 空地
WALL = 1       # 墙壁
BOX = 2        # 箱子
TARGET = 3     # 目标点
PLAYER = 4     # 玩家
BOX_ON_TARGET = 5  # 箱子在目标点上

# 颜色定义
COLORS = {
    'WHITE': (255, 255, 255),
    'BLACK': (0, 0, 0),
    'BROWN': (139, 69, 19),
    'BLUE': (0, 0, 255),
    'RED': (255, 0, 0),
    'GREEN': (0, 255, 0),
    'YELLOW': (255, 255, 0),
    'GRAY': (128, 128, 128),
    'ORANGE': (255, 165, 0),
    'PURPLE': (128, 0, 128)
}

class SokobanGame:
    """推箱子游戏主类"""
    
    def __init__(self, screen):
        """初始化游戏"""
        self.screen = screen
        self.screen_width = screen.get_width()
        self.screen_height = screen.get_height()
        
        # 游戏状态
        self.current_level = 1
        self.steps = 0
        self.game_completed = False
        self.level_completed = False

        # 玩家方向（用于选择正确的动作帧）
        self.player_direction = 'down'  # down, up, left, right
        
        # 地图相关
        self.map_data = []
        self.player_pos = [0, 0]
        self.targets = []
        self.boxes = []
        self.walls = []
        
        # 渲染相关
        self.tile_size = 40
        self.map_offset_x = 0
        self.map_offset_y = 0

        # 加载游戏素材
        self.load_sprites()
        
        # 字体 - 使用系统中文字体
        pygame.font.init()

        # 常见的Windows中文字体列表
        chinese_fonts = [
            'microsoftyahei',  # 微软雅黑
            'simhei',          # 黑体
            'simsun',          # 宋体
            'kaiti',           # 楷体
            'fangsong',        # 仿宋
            'arial unicode ms' # Arial Unicode MS
        ]

        self.font = None
        self.small_font = None

        # 尝试加载中文字体
        for font_name in chinese_fonts:
            try:
                test_font = pygame.font.SysFont(font_name, 28)
                # 测试是否能正确渲染中文
                test_surface = test_font.render('测试', True, (0, 0, 0))
                if test_surface.get_width() > 0:
                    self.font = test_font
                    self.small_font = pygame.font.SysFont(font_name, 20)
                    print(f"使用字体: {font_name}")
                    break
            except:
                continue

        # 如果没有找到合适的中文字体，使用默认字体
        if self.font is None:
            self.font = pygame.font.Font(None, 36)
            self.small_font = pygame.font.Font(None, 24)
            print("Warning: No Chinese font found, using default font")
            self.use_chinese = False
        else:
            self.use_chinese = True
        
        # 加载关卡
        self.load_level(self.current_level)

    def load_sprites(self):
        """加载游戏素材"""
        try:
            # 加载基础素材
            self.wall_sprite = pygame.image.load("image/wall.png")
            self.box_sprite = pygame.image.load("image/box.png")
            self.box_finished_sprite = pygame.image.load("image/boxfinished.png")
            self.destination_sprite = pygame.image.load("image/destination.png")

            # 加载角色素材表
            character_sheet = pygame.image.load("image/character.png")

            # 缩放基础素材到合适大小
            self.wall_sprite = pygame.transform.scale(self.wall_sprite, (self.tile_size, self.tile_size))
            self.box_sprite = pygame.transform.scale(self.box_sprite, (self.tile_size, self.tile_size))
            self.box_finished_sprite = pygame.transform.scale(self.box_finished_sprite, (self.tile_size, self.tile_size))
            self.destination_sprite = pygame.transform.scale(self.destination_sprite, (self.tile_size, self.tile_size))

            # 提取角色动作帧（假设是4x4的网格，每个方向一行）
            frame_size = 32  # 原始帧大小
            self.character_sprites = {}

            # 方向映射：下、左、右、上（常见的精灵表布局）
            directions = ['down', 'left', 'right', 'up']

            for i, direction in enumerate(directions):
                # 提取该方向的第一帧（静止状态）
                frame_rect = pygame.Rect(0, i * frame_size, frame_size, frame_size)
                frame = character_sheet.subsurface(frame_rect)
                # 缩放到游戏大小
                self.character_sprites[direction] = pygame.transform.scale(frame, (self.tile_size, self.tile_size))

            self.use_sprites = True
            print("✓ 游戏素材加载成功")

        except Exception as e:
            print(f"✗ 素材加载失败，使用默认颜色: {e}")
            self.use_sprites = False
    
    def load_level(self, level_num: int) -> bool:
        """加载指定关卡"""
        level_file = f"levels/level_{level_num}.txt"
        
        if not os.path.exists(level_file):
            # 如果文件不存在，创建默认关卡
            self.create_default_levels()
            if not os.path.exists(level_file):
                return False
        
        try:
            with open(level_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 解析地图数据
            self.map_data = []
            self.targets = []
            self.boxes = []
            self.walls = []
            
            for y, line in enumerate(lines):
                row = []
                # 不使用strip()，保持原始长度
                for x, char in enumerate(line.rstrip('\n\r')):
                    if char == '#':  # 墙壁
                        row.append(WALL)
                        self.walls.append([x, y])
                    elif char == '$':  # 箱子
                        row.append(EMPTY)  # 箱子下面是空地
                        self.boxes.append([x, y])
                    elif char == '.':  # 目标点
                        row.append(TARGET)
                        self.targets.append([x, y])
                    elif char == '@':  # 玩家
                        row.append(EMPTY)  # 玩家下面是空地
                        self.player_pos = [x, y]
                    elif char == '*':  # 箱子在目标点上
                        row.append(TARGET)
                        self.boxes.append([x, y])
                        self.targets.append([x, y])
                    elif char == '+':  # 玩家在目标点上
                        row.append(TARGET)
                        self.player_pos = [x, y]
                        self.targets.append([x, y])
                    else:  # 空地（空格或其他字符）
                        row.append(EMPTY)
                self.map_data.append(row)
            
            # 计算地图偏移，使地图居中显示
            map_width = len(self.map_data[0]) * self.tile_size
            map_height = len(self.map_data) * self.tile_size
            self.map_offset_x = (self.screen_width - map_width) // 2
            self.map_offset_y = (self.screen_height - map_height) // 2 + 30
            
            # 重置游戏状态
            self.steps = 0
            self.level_completed = False
            
            return True
            
        except Exception as e:
            print(f"加载关卡失败: {e}")
            return False
    
    def create_default_levels(self):
        """创建默认关卡文件"""
        if not os.path.exists("levels"):
            os.makedirs("levels")
        
        # 创建第一关
        level1 = """########
#......#
#.$$...#
#..@...#
#......#
########"""
        
        with open("levels/level_1.txt", 'w', encoding='utf-8') as f:
            f.write(level1)

    def handle_input(self, key):
        """处理键盘输入"""
        # 关卡完成时只允许移动（用于查看完成状态），其他按键在main.py中处理
        # 这里不需要阻止输入

        # 方向映射
        directions = {
            pygame.K_UP: (0, -1),
            pygame.K_DOWN: (0, 1),
            pygame.K_LEFT: (-1, 0),
            pygame.K_RIGHT: (1, 0),
            pygame.K_w: (0, -1),
            pygame.K_s: (0, 1),
            pygame.K_a: (-1, 0),
            pygame.K_d: (1, 0)
        }

        if key in directions:
            dx, dy = directions[key]
            # 更新玩家方向
            if dx == 0 and dy == -1:
                self.player_direction = 'up'
            elif dx == 0 and dy == 1:
                self.player_direction = 'down'
            elif dx == -1 and dy == 0:
                self.player_direction = 'left'
            elif dx == 1 and dy == 0:
                self.player_direction = 'right'

            self.move_player(dx, dy)

    def move_player(self, dx: int, dy: int):
        """移动玩家"""
        new_x = self.player_pos[0] + dx
        new_y = self.player_pos[1] + dy

        # 检查边界
        if (new_y < 0 or new_y >= len(self.map_data) or
            new_x < 0 or new_x >= len(self.map_data[0])):
            return

        # 检查目标位置
        target_tile = self.map_data[new_y][new_x]

        # 如果是墙壁，不能移动
        if target_tile == WALL:
            return

        # 如果目标位置有箱子
        if [new_x, new_y] in self.boxes:
            # 检查箱子后面的位置
            box_new_x = new_x + dx
            box_new_y = new_y + dy

            # 检查箱子新位置的边界
            if (box_new_y < 0 or box_new_y >= len(self.map_data) or
                box_new_x < 0 or box_new_x >= len(self.map_data[0])):
                return

            # 检查箱子新位置是否有障碍物
            if (self.map_data[box_new_y][box_new_x] == WALL or
                [box_new_x, box_new_y] in self.boxes):
                return

            # 移动箱子
            box_index = self.boxes.index([new_x, new_y])
            self.boxes[box_index] = [box_new_x, box_new_y]

        # 移动玩家
        self.player_pos = [new_x, new_y]
        self.steps += 1

        # 检查是否完成关卡
        self.check_level_completion()

    def check_level_completion(self):
        """检查关卡是否完成"""
        # 检查所有目标点是否都有箱子
        for target in self.targets:
            if target not in self.boxes:
                return False

        self.level_completed = True
        return True

    def reset_level(self):
        """重置当前关卡"""
        self.load_level(self.current_level)

    def next_level(self):
        """下一关"""
        if self.load_level(self.current_level + 1):
            self.current_level += 1
            return True
        else:
            self.game_completed = True
            return False

    def previous_level(self):
        """上一关"""
        if self.current_level > 1:
            self.current_level -= 1
            self.load_level(self.current_level)
            return True
        return False

    def update(self):
        """更新游戏状态"""
        pass

    def render(self):
        """渲染游戏画面"""
        # 清空屏幕
        self.screen.fill(COLORS['WHITE'])

        # 渲染地图
        self.render_map()

        # 渲染UI信息
        self.render_ui()

        # 如果关卡完成，显示完成信息
        if self.level_completed:
            self.render_completion_message()

    def render_map(self):
        """渲染地图"""
        for y, row in enumerate(self.map_data):
            for x, tile in enumerate(row):
                pos_x = self.map_offset_x + x * self.tile_size
                pos_y = self.map_offset_y + y * self.tile_size

                rect = pygame.Rect(pos_x, pos_y, self.tile_size, self.tile_size)

                if self.use_sprites:
                    # 使用素材渲染

                    # 绘制地面（目标点或普通地面）
                    if [x, y] in self.targets:
                        self.screen.blit(self.destination_sprite, (pos_x, pos_y))
                    else:
                        # 绘制灰色地面
                        pygame.draw.rect(self.screen, COLORS['GRAY'], rect)

                    # 绘制墙壁
                    if tile == WALL:
                        self.screen.blit(self.wall_sprite, (pos_x, pos_y))

                    # 绘制箱子
                    if [x, y] in self.boxes:
                        if [x, y] in self.targets:
                            self.screen.blit(self.box_finished_sprite, (pos_x, pos_y))
                        else:
                            self.screen.blit(self.box_sprite, (pos_x, pos_y))

                    # 绘制玩家
                    if [x, y] == self.player_pos:
                        self.screen.blit(self.character_sprites[self.player_direction], (pos_x, pos_y))

                else:
                    # 使用颜色块渲染（备选方案）

                    # 绘制地面
                    if [x, y] in self.targets:
                        pygame.draw.rect(self.screen, COLORS['YELLOW'], rect)
                    else:
                        pygame.draw.rect(self.screen, COLORS['GRAY'], rect)

                    # 绘制墙壁
                    if tile == WALL:
                        pygame.draw.rect(self.screen, COLORS['BROWN'], rect)

                    # 绘制箱子
                    if [x, y] in self.boxes:
                        if [x, y] in self.targets:
                            pygame.draw.rect(self.screen, COLORS['GREEN'], rect)
                        else:
                            pygame.draw.rect(self.screen, COLORS['ORANGE'], rect)

                    # 绘制玩家
                    if [x, y] == self.player_pos:
                        pygame.draw.rect(self.screen, COLORS['BLUE'], rect)

                    # 绘制边框
                    pygame.draw.rect(self.screen, COLORS['BLACK'], rect, 1)

    def render_ui(self):
        """渲染UI信息"""
        try:
            if self.use_chinese:
                # 中文界面
                level_text = self.font.render(f"关卡: {self.current_level}", True, COLORS['BLACK'])
                steps_text = self.font.render(f"步数: {self.steps}", True, COLORS['BLACK'])
                completed_boxes = sum(1 for box in self.boxes if box in self.targets)
                total_boxes = len(self.targets)
                progress_text = self.font.render(f"进度: {completed_boxes}/{total_boxes}", True, COLORS['BLACK'])
                help_text = self.small_font.render("方向键移动 | R重置 | N下一关 | P上一关 | ESC退出", True, COLORS['BLACK'])
            else:
                # 英文界面
                level_text = self.font.render(f"Level: {self.current_level}", True, COLORS['BLACK'])
                steps_text = self.font.render(f"Steps: {self.steps}", True, COLORS['BLACK'])
                completed_boxes = sum(1 for box in self.boxes if box in self.targets)
                total_boxes = len(self.targets)
                progress_text = self.font.render(f"Progress: {completed_boxes}/{total_boxes}", True, COLORS['BLACK'])
                help_text = self.small_font.render("Arrow Keys: Move | R: Reset | N: Next | P: Prev | ESC: Exit", True, COLORS['BLACK'])

            self.screen.blit(level_text, (10, 10))
            self.screen.blit(steps_text, (200, 10))
            self.screen.blit(progress_text, (400, 10))
            self.screen.blit(help_text, (10, self.screen_height - 30))

        except Exception as e:
            # 如果渲染失败，使用默认字体和英文
            print(f"Font rendering error: {e}")
            default_font = pygame.font.Font(None, 36)
            level_text = default_font.render(f"Level: {self.current_level}", True, COLORS['BLACK'])
            self.screen.blit(level_text, (10, 10))

    def render_completion_message(self):
        """渲染关卡完成信息"""
        try:
            # 创建半透明覆盖层
            overlay = pygame.Surface((self.screen_width, self.screen_height))
            overlay.set_alpha(128)
            overlay.fill(COLORS['BLACK'])
            self.screen.blit(overlay, (0, 0))

            # 完成信息
            if self.use_chinese:
                if self.game_completed:
                    message = "恭喜！所有关卡完成！"
                    hint_message = ""
                else:
                    message = f"关卡 {self.current_level} 完成！"
                    hint_message = "按 N 键进入下一关"
            else:
                if self.game_completed:
                    message = "Congratulations! All levels completed!"
                    hint_message = ""
                else:
                    message = f"Level {self.current_level} Completed!"
                    hint_message = "Press N for next level"

            text = self.font.render(message, True, COLORS['WHITE'])
            text_rect = text.get_rect(center=(self.screen_width // 2, self.screen_height // 2))
            self.screen.blit(text, text_rect)

            # 提示信息
            if not self.game_completed and hint_message:
                hint_text = self.small_font.render(hint_message, True, COLORS['WHITE'])
                hint_rect = hint_text.get_rect(center=(self.screen_width // 2, self.screen_height // 2 + 40))
                self.screen.blit(hint_text, hint_rect)

        except Exception as e:
            print(f"Completion message rendering error: {e}")
            # 使用默认字体作为备选
            default_font = pygame.font.Font(None, 36)
            text = default_font.render("Level Completed!", True, COLORS['WHITE'])
            text_rect = text.get_rect(center=(self.screen_width // 2, self.screen_height // 2))
            self.screen.blit(text, text_rect)
