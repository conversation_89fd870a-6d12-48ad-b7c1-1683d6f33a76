#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按键测试脚本
"""

import pygame
import sys

def test_keys():
    """测试按键响应"""
    pygame.init()
    
    # 创建测试窗口
    screen = pygame.display.set_mode((600, 400))
    pygame.display.set_caption("按键测试")
    
    font = pygame.font.Font(None, 36)
    small_font = pygame.font.Font(None, 24)
    
    last_key = "无"
    key_count = 0
    
    clock = pygame.time.Clock()
    running = True
    
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                key_count += 1
                
                # 记录按键
                if event.key == pygame.K_UP:
                    last_key = "上方向键"
                elif event.key == pygame.K_DOWN:
                    last_key = "下方向键"
                elif event.key == pygame.K_LEFT:
                    last_key = "左方向键"
                elif event.key == pygame.K_RIGHT:
                    last_key = "右方向键"
                elif event.key == pygame.K_w:
                    last_key = "W键"
                elif event.key == pygame.K_s:
                    last_key = "S键"
                elif event.key == pygame.K_a:
                    last_key = "A键"
                elif event.key == pygame.K_d:
                    last_key = "D键"
                elif event.key == pygame.K_r:
                    last_key = "R键"
                elif event.key == pygame.K_n:
                    last_key = "N键"
                elif event.key == pygame.K_p:
                    last_key = "P键"
                elif event.key == pygame.K_ESCAPE:
                    last_key = "ESC键"
                    running = False
                else:
                    last_key = f"其他键 ({event.key})"
        
        # 清空屏幕
        screen.fill((255, 255, 255))
        
        # 显示信息
        title = font.render("按键测试", True, (0, 0, 0))
        screen.blit(title, (50, 50))
        
        last_key_text = font.render(f"最后按键: {last_key}", True, (0, 0, 0))
        screen.blit(last_key_text, (50, 100))
        
        count_text = font.render(f"按键次数: {key_count}", True, (0, 0, 0))
        screen.blit(count_text, (50, 150))
        
        # 说明
        instructions = [
            "测试以下按键:",
            "方向键: ↑↓←→",
            "WASD: W A S D",
            "功能键: R N P",
            "ESC: 退出测试"
        ]
        
        for i, instruction in enumerate(instructions):
            text = small_font.render(instruction, True, (0, 0, 0))
            screen.blit(text, (50, 220 + i * 25))
        
        pygame.display.flip()
        clock.tick(60)
    
    pygame.quit()

if __name__ == "__main__":
    test_keys()
