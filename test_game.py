#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试推箱子游戏
"""

import pygame
import sys
from sokoban_game import SokobanGame

def test_game():
    """测试游戏基本功能"""
    print("正在测试推箱子游戏...")
    
    # 初始化Pygame
    pygame.init()
    
    # 创建测试窗口
    screen = pygame.display.set_mode((800, 600))
    pygame.display.set_caption("推箱子游戏测试")
    
    try:
        # 创建游戏实例
        game = SokobanGame(screen)
        print("✓ 游戏初始化成功")
        
        # 测试地图加载
        if game.map_data:
            print("✓ 地图加载成功")
            print(f"  地图大小: {len(game.map_data[0])} x {len(game.map_data)}")
            print(f"  玩家位置: {game.player_pos}")
            print(f"  箱子数量: {len(game.boxes)}")
            print(f"  目标点数量: {len(game.targets)}")
        else:
            print("✗ 地图加载失败")
            return False
        
        # 测试渲染
        game.render()
        pygame.display.flip()
        print("✓ 渲染测试成功")
        
        # 简单的移动测试
        original_pos = game.player_pos.copy()
        game.move_player(1, 0)  # 向右移动
        if game.player_pos != original_pos:
            print("✓ 移动功能正常")
        else:
            print("! 移动可能被阻挡（正常情况）")
        
        print("所有测试完成！游戏可以正常运行。")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        pygame.quit()

if __name__ == "__main__":
    success = test_game()
    if success:
        print("\n游戏测试通过！可以运行 python main.py 开始游戏。")
    else:
        print("\n游戏测试失败，请检查错误信息。")
        sys.exit(1)
