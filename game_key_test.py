#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏按键功能测试
"""

import pygame
import sys
from sokoban_game import SokobanGame

def test_game_keys():
    """测试游戏中的按键功能"""
    pygame.init()
    
    # 创建游戏窗口
    screen = pygame.display.set_mode((800, 600))
    pygame.display.set_caption("游戏按键测试")
    
    # 创建游戏实例
    try:
        game = SokobanGame(screen)
        print("✓ 游戏初始化成功")
    except Exception as e:
        print(f"✗ 游戏初始化失败: {e}")
        pygame.quit()
        return False
    
    clock = pygame.time.Clock()
    running = True
    
    print("\n按键测试说明:")
    print("- 方向键/WASD: 移动角色")
    print("- R键: 重置关卡")
    print("- N键: 下一关")
    print("- P键: 上一关")
    print("- ESC键: 退出")
    print("\n开始测试...")
    
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                print(f"按键: {pygame.key.name(event.key)}")
                
                if event.key == pygame.K_ESCAPE:
                    running = False
                elif event.key == pygame.K_r:
                    game.reset_level()
                    print("  → 重置关卡")
                elif event.key == pygame.K_n:
                    if game.next_level():
                        print(f"  → 进入关卡 {game.current_level}")
                    else:
                        print("  → 已经是最后一关")
                elif event.key == pygame.K_p:
                    if game.previous_level():
                        print(f"  → 返回关卡 {game.current_level}")
                    else:
                        print("  → 已经是第一关")
                elif event.key in [pygame.K_UP, pygame.K_DOWN, pygame.K_LEFT, pygame.K_RIGHT,
                                 pygame.K_w, pygame.K_s, pygame.K_a, pygame.K_d]:
                    old_pos = game.player_pos.copy()
                    old_steps = game.steps
                    game.handle_input(event.key)
                    if game.player_pos != old_pos:
                        print(f"  → 玩家移动: {old_pos} → {game.player_pos}")
                        print(f"  → 步数: {old_steps} → {game.steps}")
                    else:
                        print("  → 移动被阻挡")
                    
                    if game.level_completed:
                        print("  → 关卡完成！")
        
        # 更新和渲染
        game.update()
        game.render()
        pygame.display.flip()
        clock.tick(30)
    
    pygame.quit()
    print("\n按键测试完成")
    return True

if __name__ == "__main__":
    test_game_keys()
